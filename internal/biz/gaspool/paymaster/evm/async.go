package evm

import (
	"byd_wallet/model"
	"context"
	"fmt"
	"github.com/ethereum/go-ethereum/common"
)

func (pm *Paymaster) handleWaitGasTask(ctx context.Context, gasTransferWaitSecs int64) {
	ids, err := pm.repo.AllWaitGasTask(ctx)
	if err != nil {
		pm.log.Errorf("AllWaitGasTask: %v", err)
		return
	}
	if len(ids) == 0 {
		return
	}
	pm.log.Infof("handleWaitGasTask: count=%d", len(ids))

	g := pm.waitGasTaskPool.NewGroup()

	//nowUnix := time.Now().Unix()
	for _, id := range ids {
		g.Submit(func() {
			stx, err := pm.stxMgr.FindGasPoolSponsorTxByID(ctx, id)
			if err != nil {
				pm.log.Errorf("get sponsor tx error: %v", err)
				terr := pm.repo.RemoveWaitGasTask(ctx, id)
				if terr != nil {
					pm.log.Errorf("RemoveWaitGasTask by db fail: %v: id=%d", terr, id)
				}
				return
			}

			if stx.Status != model.GasPoolTxStatusWaitGas {
				err := pm.repo.RemoveWaitGasTask(ctx, id)
				if err != nil {
					pm.log.Errorf("RemoveWaitGasTask by useless tx: %v: id=%d", err, id)
				}
				return
			}
			confirmed, err := pm.checkGasTransferConfirmed(ctx, stx.ChainIndex, stx.TxHash)
			if !confirmed {
				pm.log.Debugf("%s链gas转账尚未确认，继续等待，交易哈希: %s",
					pm.chainName, stx.TxHash)
				return
			}
			update := &model.GasPoolSponsorTx{}
			update.ID = stx.ID

			// wait deposit tx
			if stx.TxType == model.GasPoolTxTypeDepositPreReduceGas {
				update.Status = model.GasPoolTxStatusPending
				if err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update); err != nil {
					pm.log.Errorf("UpdateGasPoolSponsorTx by wait deposit tx: %v: update=%+v", err, update)
					return
				}

				err = pm.repo.AddWaitDepositTxTask(ctx, update.ID)
				if err != nil {
					pm.log.Errorf("AddWaitDepositTxTask: %v: txID=%d", err, update.ID)
				}
				return
			}

			// finished
			update.Status = model.GasPoolTxStatusSuccess
			err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
			if err != nil {
				pm.log.Errorf("UpdateGasPoolSponsorTx by finished: %v: txID=%d, status=%s", err, update.ID, update.Status)
			}
		})
	}
	_ = g.Wait()
}
func (pm *Paymaster) handleWaitDepositTxTask(ctx context.Context, gasTransferWaitSecs int64) {
	ids, err := pm.repo.AllWaitDepositTxTask(ctx)
	if err != nil {
		pm.log.Errorf("AllDepositTask: %v", err)
		return
	}
	if len(ids) == 0 {
		return
	}
	pm.log.Infof("handleWaitDepositTask: count=%d", len(ids))

	g := pm.waitDepositTxTaskPool.NewGroup()

	//nowUnix := time.Now().Unix()
	for _, id := range ids {
		g.Submit(func() {
			stx, err := pm.stxMgr.FindGasPoolSponsorTxByID(ctx, id)
			if err != nil {
				pm.log.Errorf("get sponsor tx error: %v", err)
				terr := pm.repo.RemoveWaitDepositTxTask(ctx, id)
				if terr != nil {
					pm.log.Errorf("RemoveWaitGasTask by db fail: %v: id=%d", terr, id)
				}
				return
			}

			if stx.Status != model.GasPoolTxStatusPending {
				err := pm.repo.RemoveWaitDepositTxTask(ctx, id)
				if err != nil {
					pm.log.Errorf("RemoveWaitGasTask by useless tx: %v: id=%d", err, id)
				}
				return
			}
			update := &model.GasPoolSponsorTx{}
			update.ID = stx.ID
			confirmed, err := pm.checkGasTransferConfirmed(ctx, stx.ChainIndex, stx.TxHash)
			if !confirmed && err != nil {
				update.Status = model.GasPoolTxStatusFail
				pm.log.Debugf("%s链gas转账尚未确认，继续等待，交易哈希: %s",
					pm.chainName, stx.TxHash)
				err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
				return
			}
			if !confirmed {
				return
			}
			update.Status = model.GasPoolTxStatusSuccess
			// wait deposit tx
			if stx.TxType == model.GasPoolTxTypeDepositPreReduceGas {
				if err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update); err != nil {
					pm.log.Errorf("UpdateGasPoolSponsorTx by wait deposit tx: %v: update=%+v", err, update)
					return
				}
				// 执行gas pool充值
				depositFlow, err := pm.gpMgr.DepositGasPool(ctx, stx.UserID, stx.ValueUSDT, stx.ChainIndex, stx.TxHash)
				if err != nil {
					pm.log.Errorf("%s链执行gas pool充值失败: %v，交易ID: %d",
						pm.chainName, err, stx.ID)
					return
				}
				update.DepositFlowID = depositFlow.ID

			}

			// finished

			err = pm.stxMgr.UpdateGasPoolSponsorTx(ctx, update)
			if err != nil {
				pm.log.Errorf("UpdateGasPoolSponsorTx by finished: %v: txID=%d, status=%s", err, update.ID, update.Status)
			}
		})
	}
	_ = g.Wait()
}

// checkGasTransferConfirmed 检查gas转账交易是否已确认
//
// 功能说明：
// 1. 通过EVM客户端查询指定交易的收据信息
// 2. 验证交易是否已被区块链网络确认且执行成功
// 3. 支持多链EVM兼容，根据chainIndex选择对应的客户端
// 4. 处理交易未打包、执行失败等各种情况
//
// 参数:
//   - ctx: 上下文对象
//   - chainIndex: 链索引，用于选择对应的EVM客户端
//   - txHash: gas转账交易哈希
//
// 返回值:
//   - bool: 是否已确认（true表示已确认且成功）
//   - error: 错误信息（英文）
func (pm *Paymaster) checkGasTransferConfirmed(ctx context.Context, chainIndex int64, txHash string) (bool, error) {
	// 获取对应链的EVM客户端
	client, err := pm.evmCli.Select(chainIndex)
	if err != nil {
		return false, fmt.Errorf("failed to get EVM client for chain %s: %w", pm.chainName, err)
	}

	// 获取交易收据以检查确认状态
	receipt, err := client.TransactionReceipt(ctx, common.HexToHash(txHash))
	if err != nil {
		// 如果交易还未被打包，返回false但不报错
		// 这是正常情况，需要继续等待
		pm.log.Debugf("%s链gas转账交易尚未被打包，交易哈希: %s", pm.chainName, txHash)
		return false, nil
	}

	// 检查交易执行状态（1表示成功，0表示失败）
	if receipt.Status == 1 {
		pm.log.Debugf("%s链gas转账交易确认成功，交易哈希: %s，区块号: %d",
			pm.chainName, txHash, receipt.BlockNumber.Uint64())
		return true, nil
	} else {
		// 交易已确认但执行失败
		pm.log.Errorf("%s链gas转账交易执行失败，交易哈希: %s，区块号: %d",
			pm.chainName, txHash, receipt.BlockNumber.Uint64())
		return false, fmt.Errorf("gas transfer transaction execution failed")
	}
}
