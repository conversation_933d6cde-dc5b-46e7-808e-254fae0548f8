package data

import (
	"byd_wallet/internal/biz/gaspool/paymaster/evm"
	"context"
	"strconv"
)

type evmPaymasterRepo struct {
	*Data
}

// NewEvmPaymasterRepo 创建EVM paymaster数据访问层实例
// 参数:
//   - data: 数据访问层基础实例
func NewEvmPaymasterRepo(data *Data) evm.Repo {
	return &evmPaymasterRepo{
		Data: data,
	}
}

func (r *evmPaymasterRepo) keyWaitDepositTxTask() string {
	return "evmpm:deposittx"
}

// AllEvmGasTransferWaitConfirmRecord 获取所有EVM gas转账等待确认记录
// 从Redis哈希表中获取当前链的所有等待确认记录
func (r *evmPaymasterRepo) AllWaitDepositTxTask(ctx context.Context) ([]uint, error) {
	values, err := r.rd.SMembers(ctx, r.keyWaitDepositTxTask()).Result()
	if err != nil {
		return nil, err
	}
	ids := []uint{}
	for _, v := range values {
		id, err := strconv.Atoi(v)
		if err != nil {
			continue
		}
		ids = append(ids, uint(id))
	}
	return ids, nil
}
func (r *evmPaymasterRepo) AddWaitDepositTxTask(ctx context.Context, txID uint) error {
	idstr := strconv.Itoa(int(txID))
	return r.rd.SAdd(ctx, r.keyWaitDepositTxTask(), idstr).Err()
}

func (r *evmPaymasterRepo) RemoveWaitDepositTxTask(ctx context.Context, txID uint) error {
	idstr := strconv.Itoa(int(txID))
	return r.rd.SRem(ctx, r.keyWaitDepositTxTask(), idstr).Err()
}

func (r *evmPaymasterRepo) keyWaitGasTask() string {
	return "evmpm:waitgas"
}

func (r *evmPaymasterRepo) AddWaitGasTask(ctx context.Context, txID uint) error {
	idstr := strconv.Itoa(int(txID))
	return r.rd.SAdd(ctx, r.keyWaitGasTask(), idstr).Err()
}

func (r *evmPaymasterRepo) AllWaitGasTask(ctx context.Context) ([]uint, error) {
	values, err := r.rd.SMembers(ctx, r.keyWaitGasTask()).Result()
	if err != nil {
		return nil, err
	}
	ids := []uint{}
	for _, v := range values {
		id, err := strconv.Atoi(v)
		if err != nil {
			continue
		}
		ids = append(ids, uint(id))
	}
	return ids, nil
}

func (r *evmPaymasterRepo) RemoveWaitGasTask(ctx context.Context, txID uint) error {
	idstr := strconv.Itoa(int(txID))
	return r.rd.SRem(ctx, r.keyWaitGasTask(), idstr).Err()
}
